#!/usr/bin/env python3


import rclpy
from rclpy.node import Node
from rclpy.callback_groups import ReentrantCallbackGroup
from sensor_msgs.msg import JointState
from std_msgs.msg import Header
from ament_index_python.packages import get_package_share_directory
import os

import mujoco
import mujoco.viewer

# 导入关节角度计算函数
from scripts.FingerMathURDF import HAND_FingerPosToAngle

# Python内置函数和异常无需导入，但为防止某些环境下静态检查报错，可加如下导入（实际运行时可省略）

# 明确导入常用内置函数和异常，防止静态检查报错
from builtins import Exception, ImportError, super, range, enumerate, float, hasattr


node_name = 'rohand_mjcf_node'
DEFAULT_MJCF_PATH = os.path.join(
    get_package_share_directory('rohand_urdf_ros2'),
    'urdf/rohand_right_mj.xml'
)

# 关节与手指ID定义
THUMB_ID = 0
INDEX_FINGER_ID = 1
MIDDLE_FINGER_ID = 2
RING_FINGER_ID = 3
LITTLE_FINGER_ID = 4
THUMB_ROOT_ID = 5

JOINTS_NAME = [
    ['th_proximal_joint', 'th_slider_joint', 'th_connecting_joint', 'th_distal_joint'],
    ['if_slider_joint', 'if_slider_abpart_joint', 'if_proximal_joint', 'if_distal_joint', 'if_connecting_joint'],
    ['mf_slider_joint', 'mf_slider_abpart_joint', 'mf_proximal_joint', 'mf_distal_joint', 'mf_connecting_joint'],
    ['rf_slider_joint', 'rf_slider_abpart_joint', 'rf_proximal_joint', 'rf_distal_joint', 'rf_connecting_joint'],
    ['lf_slider_joint', 'lf_slider_abpart_joint', 'lf_proximal_joint', 'lf_distal_joint', 'lf_connecting_joint'],
    ['th_root_joint']
]


class ROHandMJCFNode(Node):
    def __init__(self):
        super().__init__(node_name)
        self.declare_parameter('mjcf_path', DEFAULT_MJCF_PATH)
        mjcf_path = self.get_parameter('mjcf_path').get_parameter_value().string_value
        self.get_logger().info('++++++++++++++ 启动 MuJoCo 仿真 ++++++++++++++')

        # 创建回调组，允许并发执行
        self.callback_group = ReentrantCallbackGroup()

        # 加载 MJCF 模型
        try:
            self.model = mujoco.MjModel.from_xml_path(mjcf_path)
            self.data = mujoco.MjData(self.model)
            self.get_logger().info(f'成功加载 MJCF 文件: {mjcf_path}')
        except Exception as e:
            self.get_logger().error(f'加载 MJCF 文件失败: {e}')
            raise

        # 获取关节名称列表
        self.joint_names = [self.model.joint(i).name for i in range(self.model.njnt)]
        self.get_logger().info(f'关节列表: {self.joint_names}')

        # 发布仿真关节状态
        self.joint_states_publisher = self.create_publisher(
            JointState, '/joint_states', 10)

        # 订阅控制指令
        self.joint_states_sub = self.create_subscription(
            JointState,
            '~/joint_states',
            self._joint_states_callback,
            10
        )

        # 初始化mujoco可视化
        self.viewer = None
        try:
            self.viewer = mujoco.viewer.launch_passive(self.model, self.data)
            self.get_logger().info('MuJoCo 可视化窗口已启动')
        except Exception as e:
            self.get_logger().warn(f'未能启动MuJoCo可视化窗口: {e}')
            
        # 创建20ms定时器来执行被动关节控制
        self.control_timer = self.create_timer(
            0.02,  # 20ms
            self.passive_joint_control,
            callback_group=self.callback_group
        )
        self.get_logger().info('被动关节控制定时器已启动')

    def cal_joint_angle(self, finger_id, position, msg):
            joint_angle = HAND_FingerPosToAngle(finger_id, position)
            msg.name.extend(JOINTS_NAME[finger_id])

            if (finger_id == THUMB_ID):
                msg.position.extend([joint_angle[0],
                                     position,
                                     joint_angle[1],
                                     joint_angle[2]])
            elif (finger_id == THUMB_ROOT_ID):
                msg.position.append(position)
            else:
                msg.position.append(position)
                msg.position.extend(joint_angle)

    def _joint_states_callback(self, msg):
        for i, name in enumerate(msg.name):
            if name in self.joint_names and ("slider_joint" in name or name == "th_root_joint"):
                # 对于滑块和大拇指根部关节，使用position执行器控制
                actuator_name = name.replace('joint', 'pos')  # 例如：从 th_slider_joint 转换为 th_slider_pos
                try:
                    actuator_id = self.model.actuator(actuator_name).id
                    # 直接设置位置控制目标
                    self.data.ctrl[actuator_id] = msg.position[i]
                    # self.get_logger().info(
                    #     f"位置控制: joint={name}, actuator={actuator_name}, "
                    #     f"target_pos={msg.position[i]}"
                    # )
                except Exception as e:
                    self.get_logger().error(f"未找到关节 {name} 对应的执行器 {actuator_name}: {e}")

    def passive_joint_control(self):
        """被动关节控制回调函数"""
        try:
            rotation_msg = JointState()
            rotation_msg.header = Header()
            rotation_msg.header.stamp = self.get_clock().now().to_msg()
            rotation_msg.name = []
            rotation_msg.position = []

            for i, name in enumerate(self.joint_names):
                j_id = self.model.joint(name).id
                qpos_addr = self.model.jnt_qposadr[j_id]
                position = self.data.qpos[qpos_addr]
                if name == 'th_root_joint':
                    if position > 1.605:
                        position = 1.605
                    if position < -0.034:
                        position = -0.034
                if name == 'th_slider_joint':
                    if position > 0.008:
                        position = 0.008
                    if position < -0.003:
                        position = -0.003
                else:
                    if position > 0.016:
                        position = 0.016
                    if position < -0.003:
                        position = -0.003

                if name == 'if_slider_joint':
                    # position = msg.position[i]
                    self.cal_joint_angle(INDEX_FINGER_ID, position, rotation_msg)

                if name == 'mf_slider_joint':
                    # position = msg.position[i]
                    self.cal_joint_angle(MIDDLE_FINGER_ID, position, rotation_msg)

                if name == 'rf_slider_joint':
                    # position = msg.position[i]
                    self.cal_joint_angle(RING_FINGER_ID, position, rotation_msg)

                if name == 'lf_slider_joint':
                    # position = msg.position[i]
                    self.cal_joint_angle(LITTLE_FINGER_ID, position, rotation_msg)

                if name == 'th_slider_joint':
                    # position = msg.position[i]
                    self.cal_joint_angle(THUMB_ID, position, rotation_msg)

                if name == 'th_root_joint':
                    # position = msg.position[i]
                    self.cal_joint_angle(THUMB_ROOT_ID, position, rotation_msg)

            # publish message
            self.joint_states_publisher.publish(rotation_msg)
            # self.get_logger().info(f'Published rotation angle: {rotation_msg.position}\n')

            for i, name in enumerate(rotation_msg.name):
                if name in self.joint_names and "slider_joint" not in name and name != "th_root_joint":
                    actuator_name = name.replace('joint', 'pos')
                    actuator_id = self.model.actuator(actuator_name).id
                    self.data.ctrl[actuator_id] = rotation_msg.position[i]

            # print(self.data.qpos)

            # 步进仿真
            mujoco.mj_step(self.model, self.data)

            # 更新可视化
            if self.viewer is not None:
                self.viewer.sync()

        except Exception as e:
            self.get_logger().warn(f'被动关节控制异常: {e}')

    def destroy_node(self):
        """清理节点资源"""
        if hasattr(self, 'control_timer'):
            self.destroy_timer(self.control_timer)
        super().destroy_node()


def main(args=None):
    rclpy.init(args=args)
    node = ROHandMJCFNode()
    rclpy.spin(node)
    node.destroy_node()
    rclpy.shutdown()
