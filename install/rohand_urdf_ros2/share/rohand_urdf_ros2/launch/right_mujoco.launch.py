import os
from launch import LaunchDescription
from launch.substitutions import LaunchConfiguration
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare


def generate_launch_description():
    package_name = "rohand_urdf_ros2"
    urdf_name = "rohand_right_mj_ros2.urdf"
    rviz_name = "rohand_right_urdf.rviz"
    mjcf_name = "rohand_right_mj.xml"

    ld = LaunchDescription()
    pkg_share = FindPackageShare(package=package_name).find(package_name)
    urdf_model_path = os.path.join(pkg_share, f"urdf/{urdf_name}")
    rviz_path = os.path.join(pkg_share, f"rviz/{rviz_name}")
    mjcf_path = os.path.join(pkg_share, f"urdf/{mjcf_name}")

    robot_state_publisher_node = Node(
        package="robot_state_publisher",
        executable="robot_state_publisher",
        name="rohand_right_state_publisher",
        namespace="rohand_right",
        parameters=[{
            "frame_prefix": "rohand_right/",
            }],
        remappings=[("/joint_states", "/rohand_right/joint_states")],
        arguments=[urdf_model_path]
    )

    joint_state_publisher_node = Node(
        package=package_name,
        executable="rohand_joint_state_gui_mj",
        name="rohand_right_joint_state_gui_mj",
        remappings=[("/joint_states", "/rohand_right/rohand_right_mjcf_node/joint_states")],
        arguments=[urdf_model_path]
    )

    # joint_state_publisher_node = Node(
    #     package=package_name,
    #     executable="rohand_joint_state_random",
    #     name="rohand_right_joint_state_random",
    #     remappings=[("/joint_states", "/rohand_right/rohand_right_mjcf_node/joint_states")],
    #     arguments=[urdf_model_path]
    # )

    # 新增rohand_mjcf_node
    rohand_mjcf_node = Node(
        package=package_name,
        executable="rohand_mjcf_node",
        name="rohand_right_mjcf_node",
        namespace="rohand_right",
        parameters=[{"mjcf_path": mjcf_path}],
        remappings=[("/joint_states", "/rohand_right/joint_states")],
        output="screen"
    )

    rviz2_node = Node(
        package="rviz2",
        executable="rviz2",
        name="rohand_left_rviz2",
        output="screen",
        arguments=["-d", rviz_path]
    )

    ld.add_action(robot_state_publisher_node)
    ld.add_action(joint_state_publisher_node)
    ld.add_action(rohand_mjcf_node)
    ld.add_action(rviz2_node)

    return ld
