[0.000000] (-) TimerEvent: {}
[0.000079] (rohand_urdf_ros2) JobQueued: {'identifier': 'rohand_urdf_ros2', 'dependencies': OrderedDict()}
[0.000102] (rohand_urdf_ros2) JobStarted: {'identifier': 'rohand_urdf_ros2'}
[0.099564] (-) TimerEvent: {}
[0.199771] (-) TimerEvent: {}
[0.299968] (-) TimerEvent: {}
[0.400140] (-) TimerEvent: {}
[0.500355] (-) TimerEvent: {}
[0.600671] (-) TimerEvent: {}
[0.700866] (-) TimerEvent: {}
[0.801029] (-) TimerEvent: {}
[0.879054] (rohand_urdf_ros2) Command: {'cmd': ['/usr/bin/python3', '-W', 'ignore:setup.py install is deprecated', '-W', 'ignore:easy_install command is deprecated', 'setup.py', 'egg_info', '--egg-base', '../../build/rohand_urdf_ros2', 'build', '--build-base', '/home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/build', 'install', '--record', '/home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/install.log', '--single-version-externally-managed', 'install_data'], 'cwd': '/home/<USER>/Grasping/mujoco/src/rohand_urdf_ros2', 'env': {'LESSOPEN': '| /usr/bin/lesspipe %s', 'CONDA_PROMPT_MODIFIER': '(env_isaaclab)', 'LANGUAGE': 'zh_CN:en_US:en', 'USER': 'wangml71', 'LC_TIME': 'zh_CN.UTF-8', 'XDG_SESSION_TYPE': 'x11', 'CLUTTER_DISABLE_MIPMAPPED_TEXT': '1', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib:/usr/local/cuda/lib64:', 'HOME': '/home/<USER>', 'CONDA_SHLVL': '1', 'OLDPWD': '/home/<USER>', 'DESKTOP_SESSION': 'ubuntu', 'ROS_PYTHON_VERSION': '3', 'GNOME_SHELL_SESSION_MODE': 'ubuntu', 'GTK_MODULES': 'gail:atk-bridge', 'SDL_IM_MODULE': 'fcitx', 'LC_MONETARY': 'zh_CN.UTF-8', 'SYSTEMD_EXEC_PID': '2984', 'GSM_SKIP_SSH_AGENT_WORKAROUND': 'true', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'COLORTERM': 'truecolor', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'IM_CONFIG_PHASE': '1', 'ROS_DISTRO': 'jazzy', 'GTK_IM_MODULE': 'fcitx', 'LOGNAME': 'wangml71', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'MEMORY_PRESSURE_WATCH': '/sys/fs/cgroup/user.slice/user-1000.slice/<EMAIL>/session.slice/<EMAIL>/memory.pressure', 'XDG_SESSION_CLASS': 'user', 'USERNAME': 'wangml71', 'TERM': 'xterm-256color', 'GNOME_DESKTOP_SESSION_ID': 'this-is-deprecated', 'WINDOWPATH': '2', 'PATH': '/home/<USER>/miniconda3/envs/env_isaaclab/bin:/home/<USER>/miniconda3/condabin:/opt/ros/jazzy/bin:/usr/local/cuda/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin', 'SESSION_MANAGER': 'local/wangml71-Z390:@/tmp/.ICE-unix/2951,unix/wangml71-Z390:/tmp/.ICE-unix/2951', 'PAPERSIZE': 'a4', 'XDG_MENU_PREFIX': 'gnome-', 'LC_ADDRESS': 'zh_CN.UTF-8', 'GNOME_TERMINAL_SCREEN': '/org/gnome/Terminal/screen/18968319_3af8_4ce0_a0bf_7b433d7dee06', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': ':1', 'LANG': 'zh_CN.UTF-8', 'XDG_CURRENT_DESKTOP': 'ubuntu:GNOME', 'LC_TELEPHONE': 'zh_CN.UTF-8', 'XMODIFIERS': '@im=fcitx', 'XDG_SESSION_DESKTOP': 'ubuntu', 'XAUTHORITY': '/run/user/1000/gdm/Xauthority', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'GNOME_TERMINAL_SERVICE': ':1.108', 'SSH_AUTH_SOCK': '/run/user/1000/keyring/ssh', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'CONDA_PYTHON_EXE': '/home/<USER>/miniconda3/bin/python', 'SHELL': '/bin/bash', 'LC_NAME': 'zh_CN.UTF-8', 'QT_ACCESSIBILITY': '1', 'GDMSESSION': 'ubuntu', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'CONDA_DEFAULT_ENV': 'env_isaaclab', 'LC_MEASUREMENT': 'zh_CN.UTF-8', 'GPG_AGENT_INFO': '/run/user/1000/gnupg/S.gpg-agent:0:1', 'LC_IDENTIFICATION': 'zh_CN.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'QT_IM_MODULE': 'fcitx', 'PWD': '/home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2', 'XDG_CONFIG_DIRS': '/etc/xdg/xdg-ubuntu:/etc/xdg', 'CONDA_EXE': '/home/<USER>/miniconda3/bin/conda', 'CLUTTER_IM_MODULE': 'fcitx', 'XDG_DATA_DIRS': '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'zh_CN.UTF-8', 'CONDA_PREFIX': '/home/<USER>/miniconda3/envs/env_isaaclab', 'LC_PAPER': 'zh_CN.UTF-8', 'COLCON': '1', 'MEMORY_PRESSURE_WRITE': 'c29tZSAyMDAwMDAgMjAwMDAwMAA=', 'VTE_VERSION': '7600', 'CMAKE_PREFIX_PATH': '/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor'}, 'shell': False}
[0.901136] (-) TimerEvent: {}
[1.001323] (-) TimerEvent: {}
[1.080718] (rohand_urdf_ros2) StdoutLine: {'line': b'running egg_info\n'}
[1.095477] (rohand_urdf_ros2) StdoutLine: {'line': b'writing ../../build/rohand_urdf_ros2/rohand_urdf_ros2.egg-info/PKG-INFO\n'}
[1.098262] (rohand_urdf_ros2) StdoutLine: {'line': b'writing dependency_links to ../../build/rohand_urdf_ros2/rohand_urdf_ros2.egg-info/dependency_links.txt\n'}
[1.098469] (rohand_urdf_ros2) StdoutLine: {'line': b'writing entry points to ../../build/rohand_urdf_ros2/rohand_urdf_ros2.egg-info/entry_points.txt\n'}
[1.098745] (rohand_urdf_ros2) StdoutLine: {'line': b'writing requirements to ../../build/rohand_urdf_ros2/rohand_urdf_ros2.egg-info/requires.txt\n'}
[1.098868] (rohand_urdf_ros2) StdoutLine: {'line': b'writing top-level names to ../../build/rohand_urdf_ros2/rohand_urdf_ros2.egg-info/top_level.txt\n'}
[1.101414] (-) TimerEvent: {}
[1.129955] (rohand_urdf_ros2) StdoutLine: {'line': b"reading manifest file '../../build/rohand_urdf_ros2/rohand_urdf_ros2.egg-info/SOURCES.txt'\n"}
[1.130480] (rohand_urdf_ros2) StdoutLine: {'line': b"adding license file 'LICENSE'\n"}
[1.131343] (rohand_urdf_ros2) StdoutLine: {'line': b"writing manifest file '../../build/rohand_urdf_ros2/rohand_urdf_ros2.egg-info/SOURCES.txt'\n"}
[1.131538] (rohand_urdf_ros2) StdoutLine: {'line': b'running build\n'}
[1.131618] (rohand_urdf_ros2) StdoutLine: {'line': b'running build_py\n'}
[1.132338] (rohand_urdf_ros2) StdoutLine: {'line': b'running install\n'}
[1.137224] (rohand_urdf_ros2) StdoutLine: {'line': b'running install_lib\n'}
[1.153199] (rohand_urdf_ros2) StdoutLine: {'line': b'running install_data\n'}
[1.155070] (rohand_urdf_ros2) StdoutLine: {'line': b'running install_egg_info\n'}
[1.171023] (rohand_urdf_ros2) StdoutLine: {'line': b"removing '/home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/python3.12/site-packages/rohand_urdf_ros2-0.0.0-py3.12.egg-info' (and everything under it)\n"}
[1.171312] (rohand_urdf_ros2) StdoutLine: {'line': b'Copying ../../build/rohand_urdf_ros2/rohand_urdf_ros2.egg-info to /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/python3.12/site-packages/rohand_urdf_ros2-0.0.0-py3.12.egg-info\n'}
[1.171942] (rohand_urdf_ros2) StdoutLine: {'line': b'running install_scripts\n'}
[1.201517] (-) TimerEvent: {}
[1.270237] (rohand_urdf_ros2) StdoutLine: {'line': b'Installing rohand_joint_state_gui script to /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/rohand_urdf_ros2\n'}
[1.270451] (rohand_urdf_ros2) StdoutLine: {'line': b'Installing rohand_joint_state_gui_mj script to /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/rohand_urdf_ros2\n'}
[1.270628] (rohand_urdf_ros2) StdoutLine: {'line': b'Installing rohand_joint_state_random script to /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/rohand_urdf_ros2\n'}
[1.270874] (rohand_urdf_ros2) StdoutLine: {'line': b'Installing rohand_mjcf_control_node script to /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/rohand_urdf_ros2\n'}
[1.270963] (rohand_urdf_ros2) StdoutLine: {'line': b'Installing rohand_mjcf_node script to /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/rohand_urdf_ros2\n'}
[1.271064] (rohand_urdf_ros2) StdoutLine: {'line': b'Installing rohand_urdf_node script to /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/rohand_urdf_ros2\n'}
[1.271307] (rohand_urdf_ros2) StdoutLine: {'line': b"writing list of installed files to '/home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/install.log'\n"}
[1.289127] (rohand_urdf_ros2) CommandEnded: {'returncode': 0}
[1.301517] (rohand_urdf_ros2) JobEnded: {'identifier': 'rohand_urdf_ros2', 'rc': 0}
[1.301820] (-) TimerEvent: {}
[1.301864] (-) EventReactorShutdown: {}
