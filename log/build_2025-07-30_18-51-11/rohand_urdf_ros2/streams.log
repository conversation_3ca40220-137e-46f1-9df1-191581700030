[0.879s] Invoking command in '/home/<USER>/Grasping/mujoco/src/rohand_urdf_ros2': CONDA_PROMPT_MODIFIER=(env_isaaclab) DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/rohand_urdf_ros2 build --build-base /home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/build install --record /home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/install.log --single-version-externally-managed install_data
[1.081s] running egg_info
[1.095s] writing ../../build/rohand_urdf_ros2/rohand_urdf_ros2.egg-info/PKG-INFO
[1.098s] writing dependency_links to ../../build/rohand_urdf_ros2/rohand_urdf_ros2.egg-info/dependency_links.txt
[1.099s] writing entry points to ../../build/rohand_urdf_ros2/rohand_urdf_ros2.egg-info/entry_points.txt
[1.099s] writing requirements to ../../build/rohand_urdf_ros2/rohand_urdf_ros2.egg-info/requires.txt
[1.099s] writing top-level names to ../../build/rohand_urdf_ros2/rohand_urdf_ros2.egg-info/top_level.txt
[1.130s] reading manifest file '../../build/rohand_urdf_ros2/rohand_urdf_ros2.egg-info/SOURCES.txt'
[1.130s] adding license file 'LICENSE'
[1.131s] writing manifest file '../../build/rohand_urdf_ros2/rohand_urdf_ros2.egg-info/SOURCES.txt'
[1.131s] running build
[1.132s] running build_py
[1.132s] running install
[1.137s] running install_lib
[1.153s] running install_data
[1.155s] running install_egg_info
[1.171s] removing '/home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/python3.12/site-packages/rohand_urdf_ros2-0.0.0-py3.12.egg-info' (and everything under it)
[1.171s] Copying ../../build/rohand_urdf_ros2/rohand_urdf_ros2.egg-info to /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/python3.12/site-packages/rohand_urdf_ros2-0.0.0-py3.12.egg-info
[1.172s] running install_scripts
[1.270s] Installing rohand_joint_state_gui script to /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/rohand_urdf_ros2
[1.270s] Installing rohand_joint_state_gui_mj script to /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/rohand_urdf_ros2
[1.271s] Installing rohand_joint_state_random script to /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/rohand_urdf_ros2
[1.271s] Installing rohand_mjcf_control_node script to /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/rohand_urdf_ros2
[1.271s] Installing rohand_mjcf_node script to /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/rohand_urdf_ros2
[1.271s] Installing rohand_urdf_node script to /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/rohand_urdf_ros2
[1.271s] writing list of installed files to '/home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/install.log'
[1.289s] Invoked command in '/home/<USER>/Grasping/mujoco/src/rohand_urdf_ros2' returned '0': CONDA_PROMPT_MODIFIER=(env_isaaclab) DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/rohand_urdf_ros2 build --build-base /home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/build install --record /home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/install.log --single-version-externally-managed install_data
