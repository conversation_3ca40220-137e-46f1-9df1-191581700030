running egg_info
writing ../../build/rohand_urdf_ros2/rohand_urdf_ros2.egg-info/PKG-INFO
writing dependency_links to ../../build/rohand_urdf_ros2/rohand_urdf_ros2.egg-info/dependency_links.txt
writing entry points to ../../build/rohand_urdf_ros2/rohand_urdf_ros2.egg-info/entry_points.txt
writing requirements to ../../build/rohand_urdf_ros2/rohand_urdf_ros2.egg-info/requires.txt
writing top-level names to ../../build/rohand_urdf_ros2/rohand_urdf_ros2.egg-info/top_level.txt
reading manifest file '../../build/rohand_urdf_ros2/rohand_urdf_ros2.egg-info/SOURCES.txt'
adding license file 'LICENSE'
writing manifest file '../../build/rohand_urdf_ros2/rohand_urdf_ros2.egg-info/SOURCES.txt'
running build
running build_py
running install
running install_lib
running install_data
running install_egg_info
removing '/home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/python3.12/site-packages/rohand_urdf_ros2-0.0.0-py3.12.egg-info' (and everything under it)
Copying ../../build/rohand_urdf_ros2/rohand_urdf_ros2.egg-info to /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/python3.12/site-packages/rohand_urdf_ros2-0.0.0-py3.12.egg-info
running install_scripts
Installing rohand_joint_state_gui script to /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/rohand_urdf_ros2
Installing rohand_joint_state_gui_mj script to /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/rohand_urdf_ros2
Installing rohand_joint_state_random script to /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/rohand_urdf_ros2
Installing rohand_mjcf_control_node script to /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/rohand_urdf_ros2
Installing rohand_mjcf_node script to /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/rohand_urdf_ros2
Installing rohand_urdf_node script to /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/rohand_urdf_ros2
writing list of installed files to '/home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/install.log'
