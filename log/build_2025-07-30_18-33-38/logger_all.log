[0.100s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.100s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=8, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x7c6377f798b0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7c637809a0c0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7c637809a0c0>>)
[0.130s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.130s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.130s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.130s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.130s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.130s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.130s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/Grasping/mujoco'
[0.130s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.130s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.130s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.130s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.130s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.130s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.130s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.130s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.130s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/rohand_urdf_ros2) by extensions ['ignore', 'ignore_ament_install']
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/rohand_urdf_ros2) by extension 'ignore'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/rohand_urdf_ros2) by extension 'ignore_ament_install'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/rohand_urdf_ros2) by extensions ['colcon_pkg']
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/rohand_urdf_ros2) by extension 'colcon_pkg'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/rohand_urdf_ros2) by extensions ['colcon_meta']
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/rohand_urdf_ros2) by extension 'colcon_meta'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/rohand_urdf_ros2) by extensions ['ros']
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/rohand_urdf_ros2) by extension 'ros'
[0.159s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rohand_urdf_ros2' with type 'ros.ament_python' and name 'rohand_urdf_ros2'
[0.160s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.160s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.160s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.160s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.160s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.173s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.173s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.176s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 285 installed packages in /opt/ros/jazzy
[0.177s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.212s] Level 5:colcon.colcon_core.verb:set package 'rohand_urdf_ros2' build argument 'cmake_args' from command line to 'None'
[0.212s] Level 5:colcon.colcon_core.verb:set package 'rohand_urdf_ros2' build argument 'cmake_target' from command line to 'None'
[0.213s] Level 5:colcon.colcon_core.verb:set package 'rohand_urdf_ros2' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.213s] Level 5:colcon.colcon_core.verb:set package 'rohand_urdf_ros2' build argument 'cmake_clean_cache' from command line to 'False'
[0.213s] Level 5:colcon.colcon_core.verb:set package 'rohand_urdf_ros2' build argument 'cmake_clean_first' from command line to 'False'
[0.213s] Level 5:colcon.colcon_core.verb:set package 'rohand_urdf_ros2' build argument 'cmake_force_configure' from command line to 'False'
[0.213s] Level 5:colcon.colcon_core.verb:set package 'rohand_urdf_ros2' build argument 'ament_cmake_args' from command line to 'None'
[0.213s] Level 5:colcon.colcon_core.verb:set package 'rohand_urdf_ros2' build argument 'catkin_cmake_args' from command line to 'None'
[0.213s] Level 5:colcon.colcon_core.verb:set package 'rohand_urdf_ros2' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.213s] DEBUG:colcon.colcon_core.verb:Building package 'rohand_urdf_ros2' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2', 'merge_install': False, 'path': '/home/<USER>/Grasping/mujoco/src/rohand_urdf_ros2', 'symlink_install': False, 'test_result_base': None}
[0.213s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.213s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.213s] INFO:colcon.colcon_ros.task.ament_python.build:Building ROS package in '/home/<USER>/Grasping/mujoco/src/rohand_urdf_ros2' with build type 'ament_python'
[0.214s] Level 1:colcon.colcon_core.shell:create_environment_hook('rohand_urdf_ros2', 'ament_prefix_path')
[0.217s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.217s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/hook/ament_prefix_path.ps1'
[0.219s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/hook/ament_prefix_path.dsv'
[0.219s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/hook/ament_prefix_path.sh'
[0.220s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.220s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.387s] INFO:colcon.colcon_core.task.python.build:Building Python package in '/home/<USER>/Grasping/mujoco/src/rohand_urdf_ros2'
[0.387s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.387s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.004s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Grasping/mujoco/src/rohand_urdf_ros2': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/rohand_urdf_ros2 build --build-base /home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/build install --record /home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/install.log --single-version-externally-managed install_data
[1.463s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Grasping/mujoco/src/rohand_urdf_ros2' returned '0': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/rohand_urdf_ros2 build --build-base /home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/build install --record /home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/install.log --single-version-externally-managed install_data
[1.471s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2' for CMake module files
[1.471s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2' for CMake config files
[1.472s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib'
[1.472s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/bin'
[1.472s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/pkgconfig/rohand_urdf_ros2.pc'
[1.472s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/python3.12/site-packages'
[1.472s] Level 1:colcon.colcon_core.shell:create_environment_hook('rohand_urdf_ros2', 'pythonpath')
[1.473s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/hook/pythonpath.ps1'
[1.473s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/hook/pythonpath.dsv'
[1.473s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/hook/pythonpath.sh'
[1.473s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/bin'
[1.473s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rohand_urdf_ros2)
[1.474s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/package.ps1'
[1.474s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/package.dsv'
[1.475s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/package.sh'
[1.475s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/package.bash'
[1.476s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/package.zsh'
[1.477s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/colcon-core/packages/rohand_urdf_ros2)
[1.477s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[1.477s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[1.477s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[1.478s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[1.482s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[1.483s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[1.483s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[1.505s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[1.506s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Grasping/mujoco/install/local_setup.ps1'
[1.506s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/Grasping/mujoco/install/_local_setup_util_ps1.py'
[1.507s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Grasping/mujoco/install/setup.ps1'
[1.508s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Grasping/mujoco/install/local_setup.sh'
[1.508s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/Grasping/mujoco/install/_local_setup_util_sh.py'
[1.509s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Grasping/mujoco/install/setup.sh'
[1.509s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Grasping/mujoco/install/local_setup.bash'
[1.510s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Grasping/mujoco/install/setup.bash'
[1.511s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Grasping/mujoco/install/local_setup.zsh'
[1.511s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Grasping/mujoco/install/setup.zsh'
