[0.000000] (-) TimerEvent: {}
[0.000263] (rohand_urdf_ros2) JobQueued: {'identifier': 'rohand_urdf_ros2', 'dependencies': OrderedDict()}
[0.000295] (rohand_urdf_ros2) JobStarted: {'identifier': 'rohand_urdf_ros2'}
[0.099467] (-) TimerEvent: {}
[0.199656] (-) TimerEvent: {}
[0.299833] (-) TimerEvent: {}
[0.400046] (-) TimerEvent: {}
[0.500252] (-) TimerEvent: {}
[0.600469] (-) TimerEvent: {}
[0.700651] (-) TimerEvent: {}
[0.789418] (rohand_urdf_ros2) Command: {'cmd': ['/usr/bin/python3', '-W', 'ignore:setup.py install is deprecated', '-W', 'ignore:easy_install command is deprecated', 'setup.py', 'egg_info', '--egg-base', '../../build/rohand_urdf_ros2', 'build', '--build-base', '/home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/build', 'install', '--record', '/home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/install.log', '--single-version-externally-managed', 'install_data'], 'cwd': '/home/<USER>/Grasping/mujoco/src/rohand_urdf_ros2', 'env': {'LESSOPEN': '| /usr/bin/lesspipe %s', 'HTTPS_PROXY': 'http://127.0.0.1:7897/', 'no_proxy': 'localhost,127.0.0.1,***********/16,10.0.0.0/8,**********/12,**********/16,::1', 'LANGUAGE': 'zh_CN:en_US:en', 'USER': 'wangml71', 'LC_TIME': 'zh_CN.UTF-8', 'all_proxy': 'socks://127.0.0.1:7897/', 'XDG_SESSION_TYPE': 'x11', 'CLUTTER_DISABLE_MIPMAPPED_TEXT': '1', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib:/usr/local/cuda/lib64:', 'HOME': '/home/<USER>', 'CONDA_SHLVL': '0', 'OLDPWD': '/home/<USER>/Grasping', 'DESKTOP_SESSION': 'ubuntu', 'NO_PROXY': 'localhost,127.0.0.1,***********/16,10.0.0.0/8,**********/12,**********/16,::1', 'ROS_PYTHON_VERSION': '3', 'GNOME_SHELL_SESSION_MODE': 'ubuntu', 'GTK_MODULES': 'gail:atk-bridge', 'SDL_IM_MODULE': 'fcitx', 'LC_MONETARY': 'zh_CN.UTF-8', 'SYSTEMD_EXEC_PID': '3075', 'GSM_SKIP_SSH_AGENT_WORKAROUND': 'true', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'COLORTERM': 'truecolor', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'IM_CONFIG_PHASE': '1', 'https_proxy': 'http://127.0.0.1:7897/', 'ROS_DISTRO': 'jazzy', 'GTK_IM_MODULE': 'fcitx', 'LOGNAME': 'wangml71', 'ALL_PROXY': 'socks://127.0.0.1:7897/', 'http_proxy': 'http://127.0.0.1:7897/', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'MEMORY_PRESSURE_WATCH': '/sys/fs/cgroup/user.slice/user-1000.slice/<EMAIL>/session.slice/<EMAIL>/memory.pressure', 'XDG_SESSION_CLASS': 'user', 'USERNAME': 'wangml71', 'TERM': 'xterm-256color', 'GNOME_DESKTOP_SESSION_ID': 'this-is-deprecated', 'WINDOWPATH': '2', 'PATH': '/home/<USER>/miniconda3/condabin:/opt/ros/jazzy/bin:/usr/local/cuda/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin', 'SESSION_MANAGER': 'local/wangml71-Z390:@/tmp/.ICE-unix/3039,unix/wangml71-Z390:/tmp/.ICE-unix/3039', 'PAPERSIZE': 'a4', 'XDG_MENU_PREFIX': 'gnome-', 'LC_ADDRESS': 'zh_CN.UTF-8', 'GNOME_TERMINAL_SCREEN': '/org/gnome/Terminal/screen/52b9f76d_1b98_4dd9_84c8_17599a2566e7', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': ':1', 'LANG': 'zh_CN.UTF-8', 'XDG_CURRENT_DESKTOP': 'ubuntu:GNOME', 'LC_TELEPHONE': 'zh_CN.UTF-8', 'XMODIFIERS': '@im=fcitx', 'XDG_SESSION_DESKTOP': 'ubuntu', 'XAUTHORITY': '/run/user/1000/gdm/Xauthority', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'GNOME_TERMINAL_SERVICE': ':1.198', 'SSH_AUTH_SOCK': '/run/user/1000/keyring/ssh', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'CONDA_PYTHON_EXE': '/home/<USER>/miniconda3/bin/python', 'SHELL': '/bin/bash', 'LC_NAME': 'zh_CN.UTF-8', 'QT_ACCESSIBILITY': '1', 'GDMSESSION': 'ubuntu', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'LC_MEASUREMENT': 'zh_CN.UTF-8', 'GPG_AGENT_INFO': '/run/user/1000/gnupg/S.gpg-agent:0:1', 'LC_IDENTIFICATION': 'zh_CN.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'QT_IM_MODULE': 'fcitx', 'PWD': '/home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2', 'XDG_CONFIG_DIRS': '/etc/xdg/xdg-ubuntu:/etc/xdg', 'CONDA_EXE': '/home/<USER>/miniconda3/bin/conda', 'CLUTTER_IM_MODULE': 'fcitx', 'XDG_DATA_DIRS': '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'HTTP_PROXY': 'http://127.0.0.1:7897/', 'LC_NUMERIC': 'zh_CN.UTF-8', 'LC_PAPER': 'zh_CN.UTF-8', 'COLCON': '1', 'MEMORY_PRESSURE_WRITE': 'c29tZSAyMDAwMDAgMjAwMDAwMAA=', 'VTE_VERSION': '7600', 'CMAKE_PREFIX_PATH': '/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor'}, 'shell': False}
[0.800730] (-) TimerEvent: {}
[0.900927] (-) TimerEvent: {}
[1.001133] (-) TimerEvent: {}
[1.005407] (rohand_urdf_ros2) StdoutLine: {'line': b'running egg_info\n'}
[1.005685] (rohand_urdf_ros2) StdoutLine: {'line': b'creating ../../build/rohand_urdf_ros2/rohand_urdf_ros2.egg-info\n'}
[1.021327] (rohand_urdf_ros2) StdoutLine: {'line': b'writing ../../build/rohand_urdf_ros2/rohand_urdf_ros2.egg-info/PKG-INFO\n'}
[1.021467] (rohand_urdf_ros2) StdoutLine: {'line': b'writing dependency_links to ../../build/rohand_urdf_ros2/rohand_urdf_ros2.egg-info/dependency_links.txt\n'}
[1.021684] (rohand_urdf_ros2) StdoutLine: {'line': b'writing entry points to ../../build/rohand_urdf_ros2/rohand_urdf_ros2.egg-info/entry_points.txt\n'}
[1.021856] (rohand_urdf_ros2) StdoutLine: {'line': b'writing requirements to ../../build/rohand_urdf_ros2/rohand_urdf_ros2.egg-info/requires.txt\n'}
[1.021971] (rohand_urdf_ros2) StdoutLine: {'line': b'writing top-level names to ../../build/rohand_urdf_ros2/rohand_urdf_ros2.egg-info/top_level.txt\n'}
[1.022032] (rohand_urdf_ros2) StdoutLine: {'line': b"writing manifest file '../../build/rohand_urdf_ros2/rohand_urdf_ros2.egg-info/SOURCES.txt'\n"}
[1.054601] (rohand_urdf_ros2) StdoutLine: {'line': b"reading manifest file '../../build/rohand_urdf_ros2/rohand_urdf_ros2.egg-info/SOURCES.txt'\n"}
[1.054779] (rohand_urdf_ros2) StdoutLine: {'line': b"adding license file 'LICENSE'\n"}
[1.055444] (rohand_urdf_ros2) StdoutLine: {'line': b"writing manifest file '../../build/rohand_urdf_ros2/rohand_urdf_ros2.egg-info/SOURCES.txt'\n"}
[1.055571] (rohand_urdf_ros2) StdoutLine: {'line': b'running build\n'}
[1.055648] (rohand_urdf_ros2) StdoutLine: {'line': b'running build_py\n'}
[1.055723] (rohand_urdf_ros2) StdoutLine: {'line': b'creating /home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/build\n'}
[1.055783] (rohand_urdf_ros2) StdoutLine: {'line': b'creating /home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/build/lib\n'}
[1.055937] (rohand_urdf_ros2) StdoutLine: {'line': b'creating /home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/build/lib/scripts\n'}
[1.056006] (rohand_urdf_ros2) StdoutLine: {'line': b'copying scripts/rohand_mjcf_control.py -> /home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/build/lib/scripts\n'}
[1.056091] (rohand_urdf_ros2) StdoutLine: {'line': b'copying scripts/__init__.py -> /home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/build/lib/scripts\n'}
[1.056164] (rohand_urdf_ros2) StdoutLine: {'line': b'copying scripts/rohand_urdf.py -> /home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/build/lib/scripts\n'}
[1.056226] (rohand_urdf_ros2) StdoutLine: {'line': b'copying scripts/FingerMathURDF.py -> /home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/build/lib/scripts\n'}
[1.056301] (rohand_urdf_ros2) StdoutLine: {'line': b'copying scripts/rohand_joint_state_random.py -> /home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/build/lib/scripts\n'}
[1.056372] (rohand_urdf_ros2) StdoutLine: {'line': b'copying scripts/rohand_mjcf.py -> /home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/build/lib/scripts\n'}
[1.056433] (rohand_urdf_ros2) StdoutLine: {'line': b'copying scripts/rohand_joint_state_gui.py -> /home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/build/lib/scripts\n'}
[1.056508] (rohand_urdf_ros2) StdoutLine: {'line': b'copying scripts/rohand_joint_state_gui_mj.py -> /home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/build/lib/scripts\n'}
[1.056593] (rohand_urdf_ros2) StdoutLine: {'line': b'running install\n'}
[1.061932] (rohand_urdf_ros2) StdoutLine: {'line': b'running install_lib\n'}
[1.077991] (rohand_urdf_ros2) StdoutLine: {'line': b'creating /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/python3.12/site-packages/scripts\n'}
[1.078231] (rohand_urdf_ros2) StdoutLine: {'line': b'copying /home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/build/lib/scripts/rohand_mjcf_control.py -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/python3.12/site-packages/scripts\n'}
[1.078306] (rohand_urdf_ros2) StdoutLine: {'line': b'copying /home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/build/lib/scripts/__init__.py -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/python3.12/site-packages/scripts\n'}
[1.078401] (rohand_urdf_ros2) StdoutLine: {'line': b'copying /home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/build/lib/scripts/rohand_urdf.py -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/python3.12/site-packages/scripts\n'}
[1.078467] (rohand_urdf_ros2) StdoutLine: {'line': b'copying /home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/build/lib/scripts/FingerMathURDF.py -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/python3.12/site-packages/scripts\n'}
[1.078571] (rohand_urdf_ros2) StdoutLine: {'line': b'copying /home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/build/lib/scripts/rohand_joint_state_random.py -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/python3.12/site-packages/scripts\n'}
[1.078644] (rohand_urdf_ros2) StdoutLine: {'line': b'copying /home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/build/lib/scripts/rohand_mjcf.py -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/python3.12/site-packages/scripts\n'}
[1.078701] (rohand_urdf_ros2) StdoutLine: {'line': b'copying /home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/build/lib/scripts/rohand_joint_state_gui.py -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/python3.12/site-packages/scripts\n'}
[1.078756] (rohand_urdf_ros2) StdoutLine: {'line': b'copying /home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/build/lib/scripts/rohand_joint_state_gui_mj.py -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/python3.12/site-packages/scripts\n'}
[1.078985] (rohand_urdf_ros2) StdoutLine: {'line': b'byte-compiling /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/python3.12/site-packages/scripts/rohand_mjcf_control.py to rohand_mjcf_control.cpython-312.pyc\n'}
[1.080277] (rohand_urdf_ros2) StdoutLine: {'line': b'byte-compiling /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/python3.12/site-packages/scripts/__init__.py to __init__.cpython-312.pyc\n'}
[1.080431] (rohand_urdf_ros2) StdoutLine: {'line': b'byte-compiling /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/python3.12/site-packages/scripts/rohand_urdf.py to rohand_urdf.cpython-312.pyc\n'}
[1.081181] (rohand_urdf_ros2) StdoutLine: {'line': b'byte-compiling /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/python3.12/site-packages/scripts/FingerMathURDF.py to FingerMathURDF.cpython-312.pyc\n'}
[1.082443] (rohand_urdf_ros2) StdoutLine: {'line': b'byte-compiling /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/python3.12/site-packages/scripts/rohand_joint_state_random.py to rohand_joint_state_random.cpython-312.pyc\n'}
[1.083960] (rohand_urdf_ros2) StdoutLine: {'line': b'byte-compiling /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/python3.12/site-packages/scripts/rohand_mjcf.py to rohand_mjcf.cpython-312.pyc\n'}
[1.085107] (rohand_urdf_ros2) StdoutLine: {'line': b'byte-compiling /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/python3.12/site-packages/scripts/rohand_joint_state_gui.py to rohand_joint_state_gui.cpython-312.pyc\n'}
[1.086433] (rohand_urdf_ros2) StdoutLine: {'line': b'byte-compiling /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/python3.12/site-packages/scripts/rohand_joint_state_gui_mj.py to rohand_joint_state_gui_mj.cpython-312.pyc\n'}
[1.087784] (rohand_urdf_ros2) StdoutLine: {'line': b'running install_data\n'}
[1.087966] (rohand_urdf_ros2) StdoutLine: {'line': b'creating /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/ament_index\n'}
[1.088058] (rohand_urdf_ros2) StdoutLine: {'line': b'creating /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/ament_index/resource_index\n'}
[1.088124] (rohand_urdf_ros2) StdoutLine: {'line': b'creating /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/ament_index/resource_index/packages\n'}
[1.088237] (rohand_urdf_ros2) StdoutLine: {'line': b'copying resource/rohand_urdf_ros2 -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/ament_index/resource_index/packages\n'}
[1.088304] (rohand_urdf_ros2) StdoutLine: {'line': b'copying package.xml -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2\n'}
[1.088382] (rohand_urdf_ros2) StdoutLine: {'line': b'creating /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/launch\n'}
[1.088440] (rohand_urdf_ros2) StdoutLine: {'line': b'copying launch/left_rviz2.launch.py -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/launch\n'}
[1.088515] (rohand_urdf_ros2) StdoutLine: {'line': b'copying launch/right_rviz2.launch.py -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/launch\n'}
[1.088577] (rohand_urdf_ros2) StdoutLine: {'line': b'copying launch/right_mujoco.launch.py -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/launch\n'}
[1.088636] (rohand_urdf_ros2) StdoutLine: {'line': b'creating /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/urdf\n'}
[1.088708] (rohand_urdf_ros2) StdoutLine: {'line': b'copying urdf/rohand_right_mj_fb_bak.xml -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/urdf\n'}
[1.088768] (rohand_urdf_ros2) StdoutLine: {'line': b'copying urdf/rohand_right.urdf -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/urdf\n'}
[1.088843] (rohand_urdf_ros2) StdoutLine: {'line': b'copying urdf/rohand_left.csv -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/urdf\n'}
[1.088899] (rohand_urdf_ros2) StdoutLine: {'line': b'copying urdf/rohand_right_mj_ros2.urdf -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/urdf\n'}
[1.088969] (rohand_urdf_ros2) StdoutLine: {'line': b'copying urdf/rohand_right_mj.urdf -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/urdf\n'}
[1.089029] (rohand_urdf_ros2) StdoutLine: {'line': b'copying urdf/rohand_right.csv -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/urdf\n'}
[1.089112] (rohand_urdf_ros2) StdoutLine: {'line': b'copying urdf/MUJOCO_LOG.TXT -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/urdf\n'}
[1.089185] (rohand_urdf_ros2) StdoutLine: {'line': b'copying urdf/rohand_right_mj_fb.xml -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/urdf\n'}
[1.089245] (rohand_urdf_ros2) StdoutLine: {'line': b'copying urdf/rohand_left.urdf -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/urdf\n'}
[1.089314] (rohand_urdf_ros2) StdoutLine: {'line': b'copying urdf/rohand_right_mj.xml -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/urdf\n'}
[1.089386] (rohand_urdf_ros2) StdoutLine: {'line': b'creating /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_l\n'}
[1.089447] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_l/th_root_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_l\n'}
[1.089518] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_l/rf_connecting_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_l\n'}
[1.089663] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_l/lf_slider_abpart_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_l\n'}
[1.089811] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_l/mf_slider_abpart_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_l\n'}
[1.090013] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_l/th_slider_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_l\n'}
[1.090096] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_l/lf_connecting_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_l\n'}
[1.090185] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_l/mf_proximal_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_l\n'}
[1.090424] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_l/if_slider_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_l\n'}
[1.090591] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_l/lf_distal_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_l\n'}
[1.091069] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_l/rf_proximal_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_l\n'}
[1.091279] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_l/rf_slider_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_l\n'}
[1.091393] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_l/if_distal_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_l\n'}
[1.091949] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_l/if_slider_abpart_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_l\n'}
[1.092129] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_l/if_connecting_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_l\n'}
[1.092226] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_l/th_proximal_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_l\n'}
[1.092478] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_l/rf_slider_abpart_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_l\n'}
[1.092634] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_l/lf_slider_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_l\n'}
[1.092784] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_l/rf_distal_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_l\n'}
[1.093176] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_l/base_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_l\n'}
[1.094683] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_l/th_connecting_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_l\n'}
[1.094837] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_l/lf_proximal_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_l\n'}
[1.095086] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_l/mf_connecting_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_l\n'}
[1.095263] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_l/if_proximal_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_l\n'}
[1.095352] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_l/mf_distal_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_l\n'}
[1.095848] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_l/th_distal_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_l\n'}
[1.096424] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_l/mf_slider_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_l\n'}
[1.096504] (rohand_urdf_ros2) StdoutLine: {'line': b'creating /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_r\n'}
[1.096655] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_r/th_root_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_r\n'}
[1.096820] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_r/rf_connecting_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_r\n'}
[1.096921] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_r/lf_slider_abpart_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_r\n'}
[1.096989] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_r/mf_slider_abpart_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_r\n'}
[1.097098] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_r/th_slider_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_r\n'}
[1.097187] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_r/lf_connecting_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_r\n'}
[1.097261] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_r/mf_proximal_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_r\n'}
[1.097421] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_r/if_slider_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_r\n'}
[1.097526] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_r/lf_distal_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_r\n'}
[1.097971] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_r/rf_proximal_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_r\n'}
[1.098185] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_r/rf_slider_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_r\n'}
[1.098316] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_r/if_distal_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_r\n'}
[1.098923] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_r/if_slider_abpart_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_r\n'}
[1.099076] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_r/if_connecting_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_r\n'}
[1.099148] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_r/th_proximal_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_r\n'}
[1.099446] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_r/rf_slider_abpart_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_r\n'}
[1.099587] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_r/lf_slider_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_r\n'}
[1.099783] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_r/rf_distal_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_r\n'}
[1.100389] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_r/base_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_r\n'}
[1.101231] (-) TimerEvent: {}
[1.101999] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_r/th_connecting_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_r\n'}
[1.102181] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_r/lf_proximal_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_r\n'}
[1.102402] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_r/mf_connecting_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_r\n'}
[1.102505] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_r/if_proximal_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_r\n'}
[1.102718] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_r/mf_distal_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_r\n'}
[1.103216] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_r/th_distal_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_r\n'}
[1.103787] (rohand_urdf_ros2) StdoutLine: {'line': b'copying meshes_r/mf_slider_link.STL -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/meshes_r\n'}
[1.103955] (rohand_urdf_ros2) StdoutLine: {'line': b'creating /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/rviz\n'}
[1.104047] (rohand_urdf_ros2) StdoutLine: {'line': b'copying rviz/rohand_right_urdf.rviz -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/rviz\n'}
[1.104168] (rohand_urdf_ros2) StdoutLine: {'line': b'copying rviz/rohand_left_urdf.rviz -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/rviz\n'}
[1.104276] (rohand_urdf_ros2) StdoutLine: {'line': b'creating /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/scripts\n'}
[1.104346] (rohand_urdf_ros2) StdoutLine: {'line': b'copying scripts/rohand_mjcf_control.py -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/scripts\n'}
[1.104410] (rohand_urdf_ros2) StdoutLine: {'line': b'copying scripts/__init__.py -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/scripts\n'}
[1.104486] (rohand_urdf_ros2) StdoutLine: {'line': b'copying scripts/rohand_urdf.py -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/scripts\n'}
[1.104564] (rohand_urdf_ros2) StdoutLine: {'line': b'copying scripts/FingerMathURDF.py -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/scripts\n'}
[1.104625] (rohand_urdf_ros2) StdoutLine: {'line': b'copying scripts/rohand_joint_state_random.py -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/scripts\n'}
[1.104708] (rohand_urdf_ros2) StdoutLine: {'line': b'copying scripts/rohand_mjcf.py -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/scripts\n'}
[1.104767] (rohand_urdf_ros2) StdoutLine: {'line': b'copying scripts/rohand_joint_state_gui.py -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/scripts\n'}
[1.104820] (rohand_urdf_ros2) StdoutLine: {'line': b'copying scripts/rohand_joint_state_gui_mj.py -> /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/share/rohand_urdf_ros2/scripts\n'}
[1.104873] (rohand_urdf_ros2) StdoutLine: {'line': b'running install_egg_info\n'}
[1.122158] (rohand_urdf_ros2) StdoutLine: {'line': b'Copying ../../build/rohand_urdf_ros2/rohand_urdf_ros2.egg-info to /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/python3.12/site-packages/rohand_urdf_ros2-0.0.0-py3.12.egg-info\n'}
[1.122778] (rohand_urdf_ros2) StdoutLine: {'line': b'running install_scripts\n'}
[1.201342] (-) TimerEvent: {}
[1.227952] (rohand_urdf_ros2) StdoutLine: {'line': b'Installing rohand_joint_state_gui script to /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/rohand_urdf_ros2\n'}
[1.228146] (rohand_urdf_ros2) StdoutLine: {'line': b'Installing rohand_joint_state_gui_mj script to /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/rohand_urdf_ros2\n'}
[1.228295] (rohand_urdf_ros2) StdoutLine: {'line': b'Installing rohand_joint_state_random script to /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/rohand_urdf_ros2\n'}
[1.228419] (rohand_urdf_ros2) StdoutLine: {'line': b'Installing rohand_mjcf_control_node script to /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/rohand_urdf_ros2\n'}
[1.228492] (rohand_urdf_ros2) StdoutLine: {'line': b'Installing rohand_mjcf_node script to /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/rohand_urdf_ros2\n'}
[1.228559] (rohand_urdf_ros2) StdoutLine: {'line': b'Installing rohand_urdf_node script to /home/<USER>/Grasping/mujoco/install/rohand_urdf_ros2/lib/rohand_urdf_ros2\n'}
[1.228637] (rohand_urdf_ros2) StdoutLine: {'line': b"writing list of installed files to '/home/<USER>/Grasping/mujoco/build/rohand_urdf_ros2/install.log'\n"}
[1.249050] (rohand_urdf_ros2) CommandEnded: {'returncode': 0}
[1.263590] (rohand_urdf_ros2) JobEnded: {'identifier': 'rohand_urdf_ros2', 'rc': 0}
[1.264065] (-) EventReactorShutdown: {}
